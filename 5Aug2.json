{
  "hfi_id": "",
  "interface": "pr_writer_reward_hack",
  "sheet_row_link": "",
  "compare_link": "",
  "lt_link": "",
  "root_gdrive": "",
  "task_duration": 240,
  "stage_1": {
    "pr_key_components": [
      
    ],
    "modified_components": [
      
    ],
    "modification_process": "",
    "modified_zip": "",
    "failing_tests_ss": ""

  },
  "stage_2": {
    "final_comment": "",
    "failure_category": [],
    "total_turns": ,
    "final_zip": "",
    "final_ss": ""
  },
  "prompts": [
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": ,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    },
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": ,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    },
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": ,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    },
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": ,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    }
  ]
}